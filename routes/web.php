<?php

use App\Http\Controllers\PaymentsController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Test PDF route (remove in production)
Route::get('/test-pdf', function () {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <title>Test PDF</title>
        <style>
            body { font-family: Arial, sans-serif; font-size: 14px; }
            .header { text-align: center; margin-bottom: 20px; }
            .content { margin: 20px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Test PDF Generation</h1>
        </div>
        <div class="content">
            <p>This is a test PDF to verify DomPDF is working correctly.</p>
            <p>Current time: ' . now()->format('Y-m-d H:i:s') . '</p>
            <p>Currency test: ' . formatCurrency(1234.56) . '</p>
        </div>
    </body>
    </html>';

    $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadHTML($html);
    $pdf->setPaper('A4', 'portrait');
    $pdf->setOptions([
        'isHtml5ParserEnabled' => false,
        'isPhpEnabled' => false,
        'defaultFont' => 'Arial',
        'isRemoteEnabled' => false,
    ]);

    return response($pdf->output(), 200, [
        'Content-Type' => 'application/pdf',
        'Content-Disposition' => 'inline; filename="test.pdf"',
    ]);
})->middleware('auth');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Progress tracking routes
    Route::get('/progress/{sessionId}/stream', [App\Http\Controllers\ProgressController::class, 'stream'])->name('progress.stream');
    Route::get('/progress/{sessionId}/status', [App\Http\Controllers\ProgressController::class, 'status'])->name('progress.status');
    Route::delete('/progress/{sessionId}', [App\Http\Controllers\ProgressController::class, 'clear'])->name('progress.clear');
});

Route::post('/validation', [PaymentsController::class, 'validation']);
Route::get('/registerUrl', [PaymentsController::class, 'registerUrl']);
Route::post('/confirmation', [PaymentsController::class, 'confirmation']);

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/bandwidth.php';
require __DIR__.'/network.php';
require __DIR__.'/services.php';
require __DIR__.'/customers.php';
require __DIR__.'/subscriptions.php';
require __DIR__.'/invoices.php';
