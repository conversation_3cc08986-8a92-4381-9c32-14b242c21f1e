<?php

use App\Http\Controllers\PaymentsController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [\App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Progress tracking routes
    Route::get('/progress/{sessionId}/stream', [App\Http\Controllers\ProgressController::class, 'stream'])->name('progress.stream');
    Route::get('/progress/{sessionId}/status', [App\Http\Controllers\ProgressController::class, 'status'])->name('progress.status');
    Route::delete('/progress/{sessionId}', [App\Http\Controllers\ProgressController::class, 'clear'])->name('progress.clear');
});

Route::post('/validation', [PaymentsController::class, 'validation']);
Route::get('/registerUrl', [PaymentsController::class, 'registerUrl']);
Route::post('/confirmation', [PaymentsController::class, 'confirmation']);

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
require __DIR__.'/bandwidth.php';
require __DIR__.'/network.php';
require __DIR__.'/services.php';
require __DIR__.'/customers.php';
require __DIR__.'/subscriptions.php';
require __DIR__.'/invoices.php';
