import { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import axios from 'axios';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  ArrowLeft,
  Edit,
  Trash,
  UserPlus,
  FileText,
  Mail,
  Phone,
  MapPin,
  Calendar,
  UserX,
  UserCheck,
  Pause,
  Play,
  Activity,
  Download,
  Upload,
  BarChart3,
  Plus,
  Zap,
  ChevronDown
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import {
  PermissionGate,
  PERMISSIONS
} from '@/components/permissions';

interface BandwidthUsage {
  id: number;
  download: number;
  upload: number;
  total: number;
  period_start: string;
  period_end: string;
  service_type: string;
  service_name: string;
}

interface UsageSummary {
  total_download: number;
  total_upload: number;
  total_bandwidth: number;
  current_month: number;
  last_month: number;
  recent_usage: BandwidthUsage[];
}

interface Subscription {
  id: number;
  name: string;
  description: string | null;
  status: string;
  price: string;
  billing_cycle: string;
  start_date: string;
  end_date: string | null;
  next_billing_date: string;
}

interface Invoice {
  id: number;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  total_amount: string;
  status: string;
}

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  postal_code: string | null;
  country: string | null;
  status: string;
  created_at: string;
  updated_at: string;
  full_address: string;
  subscriptions: Subscription[];
  invoices: Invoice[];
}

interface CustomerShowProps {
  customer: Customer;
}

export default function CustomerShow({ customer }: CustomerShowProps) {
  const [usageSummary, setUsageSummary] = useState<UsageSummary | null>(null);
  const [loadingUsage, setLoadingUsage] = useState<boolean>(false);

  // Fetch customer bandwidth usage
  const fetchCustomerUsage = async () => {
    setLoadingUsage(true);
    try {
      const response = await axios.get('/api/bandwidth/usage/get', {
        params: {
          customer_id: customer.id,
          period: 'month'
        }
      });

      const data = response.data;

      // Debug logging
      console.log('🔍 Customer Usage API Response:', data);
      console.log('🔍 Customer ID:', customer.id);
      console.log('🔍 Response Status:', response.status);

      // Transform the data
      const summary: UsageSummary = {
        total_download: data.summary?.total_download || 0,
        total_upload: data.summary?.total_upload || 0,
        total_bandwidth: data.summary?.total_bandwidth || 0,
        current_month: data.summary?.current_month || 0,
        last_month: data.summary?.last_month || 0,
        recent_usage: data.usage?.slice(0, 10) || [],
      };

      console.log('🔍 Transformed Summary:', summary);
      setUsageSummary(summary);
    } catch (error: any) {
      console.error('❌ Error fetching customer usage:', error);
      console.error('❌ Error details:', error.response?.data || error.message);
      setUsageSummary({
        total_download: 0,
        total_upload: 0,
        total_bandwidth: 0,
        current_month: 0,
        last_month: 0,
        recent_usage: [],
      });
    } finally {
      setLoadingUsage(false);
    }
  };

  // Format bytes for display
  const formatBytes = (bytes: number): string => {
    if (bytes >= 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    } else if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    } else if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    } else {
      return `${bytes} B`;
    }
  };

  // Load usage data on component mount
  useEffect(() => {
    fetchCustomerUsage();
  }, [customer.id]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-yellow-500">Inactive</Badge>;
      case 'suspended':
        return <Badge className="bg-red-500">Suspended</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getInvoiceStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'overdue':
        return <Badge className="bg-red-500">Overdue</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-500">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this customer?')) {
      router.delete(route('customers.destroy', customer.id));
    }
  };

  const handleSuspend = () => {
    if (confirm('Are you sure you want to suspend this customer? This will suspend all their services.')) {
      router.put(route('customers.update', customer.id), {
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        city: customer.city,
        state: customer.state,
        postal_code: customer.postal_code,
        country: customer.country,
        status: 'suspended'
      });
    }
  };

  const handleActivate = () => {
    if (confirm('Are you sure you want to activate this customer? This will activate all their services.')) {
      router.put(route('customers.update', customer.id), {
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        city: customer.city,
        state: customer.state,
        postal_code: customer.postal_code,
        country: customer.country,
        status: 'active'
      });
    }
  };

  return (
    <AppLayout>
      <Head title={`Customer: ${customer.name}`} />

      <div className="page-container">
        {/* Header Section */}
        <div className="page-header">
          <div className="page-title">
            <div className="flex items-center mb-2">
              <Button variant="ghost" asChild className="mr-2">
                <Link href={route('customers.index')}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Customers
                </Link>
              </Button>
            </div>
            <div className="flex items-center gap-3">
              <h1>{customer.name}</h1>
              {getStatusBadge(customer.status)}
            </div>
            <p>Customer details and service management</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={route('customers.edit', customer.id)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            {customer.status === 'active' ? (
              <Button variant="outline" onClick={handleSuspend} className="text-orange-600 border-orange-600 hover:bg-orange-50">
                <Pause className="h-4 w-4 mr-2" />
                Suspend
              </Button>
            ) : (
              <Button variant="outline" onClick={handleActivate} className="text-green-600 border-green-600 hover:bg-green-50">
                <Play className="h-4 w-4 mr-2" />
                Activate
              </Button>
            )}
            <Button variant="destructive" onClick={handleDelete}>
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            {/* Customer Details */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Details</CardTitle>
                <CardDescription>Basic information about the customer</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <Mail className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Email</div>
                      <div>{customer.email}</div>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Phone className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Phone</div>
                      <div>{customer.phone || 'Not provided'}</div>
                    </div>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-gray-500">Address</div>
                    <div>{customer.full_address || 'No address provided'}</div>
                  </div>
                </div>

                <div className="flex items-start">
                  <Calendar className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                  <div>
                    <div className="text-sm font-medium text-gray-500">Customer Since</div>
                    <div>{formatDate(customer.created_at)}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Subscriptions */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Subscriptions</CardTitle>
                  <CardDescription>Active and past subscriptions</CardDescription>
                </div>
                <PermissionGate permission={PERMISSIONS.SUBSCRIPTIONS.CREATE}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button className="btn-gradient">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Subscription
                        <ChevronDown className="h-4 w-4 ml-2" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`${route('subscriptions.streamlined.create')}?customer_id=${customer.id}`}>
                          <Zap className="h-4 w-4 mr-2" />
                          From Plan
                          <span className="ml-auto text-xs text-muted-foreground">Recommended</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={route('customers.subscriptions.create', customer.id)}>
                          <UserPlus className="h-4 w-4 mr-2" />
                          Manual Create
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </PermissionGate>
              </CardHeader>
              <CardContent>
                {customer.subscriptions.length === 0 ? (
                  <div className="text-center py-6 text-gray-500">
                    No subscriptions found for this customer.
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Price</TableHead>
                        <TableHead>Billing Cycle</TableHead>
                        <TableHead>Next Billing</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {customer.subscriptions.map((subscription) => (
                        <TableRow key={subscription.id}>
                          <TableCell className="font-medium">
                            <Link href={route('subscriptions.show', subscription.id)} className="hover:underline">
                              {subscription.name}
                            </Link>
                          </TableCell>
                          <TableCell>${subscription.price}</TableCell>
                          <TableCell className="capitalize">{subscription.billing_cycle}</TableCell>
                          <TableCell>{formatDate(subscription.next_billing_date)}</TableCell>
                          <TableCell>{getStatusBadge(subscription.status)}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={route('subscriptions.show', subscription.id)}>
                                View
                              </Link>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
              <CardFooter className="flex gap-2">
                <Button variant="outline" asChild className="flex-1">
                  <Link href={route('customers.subscriptions.index', customer.id)}>
                    View All Subscriptions
                  </Link>
                </Button>
                <PermissionGate permission={PERMISSIONS.SUBSCRIPTIONS.CREATE}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <Plus className="h-4 w-4" />
                        <span className="sr-only">Add Subscription</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`${route('subscriptions.streamlined.create')}?customer_id=${customer.id}`}>
                          <Zap className="h-4 w-4 mr-2" />
                          From Plan
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={route('customers.subscriptions.create', customer.id)}>
                          <UserPlus className="h-4 w-4 mr-2" />
                          Manual Create
                        </Link>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </PermissionGate>
              </CardFooter>
            </Card>

            {/* Recent Invoices */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Recent Invoices</CardTitle>
                  <CardDescription>Latest billing information</CardDescription>
                </div>
                <Button asChild>
                  <Link href={route('customers.invoices.create', customer.id)}>
                    <FileText className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                {customer.invoices.length === 0 ? (
                  <div className="text-center py-6 text-gray-500">
                    No invoices found for this customer.
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice #</TableHead>
                        <TableHead>Issue Date</TableHead>
                        <TableHead>Due Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {customer.invoices.slice(0, 5).map((invoice) => (
                        <TableRow key={invoice.id}>
                          <TableCell className="font-medium">
                            <Link href={route('invoices.show', invoice.id)} className="hover:underline">
                              {invoice.invoice_number}
                            </Link>
                          </TableCell>
                          <TableCell>{formatDate(invoice.issue_date)}</TableCell>
                          <TableCell>{formatDate(invoice.due_date)}</TableCell>
                          <TableCell>${invoice.total_amount}</TableCell>
                          <TableCell>{getInvoiceStatusBadge(invoice.status)}</TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" asChild>
                              <Link href={route('invoices.show', invoice.id)}>
                                View
                              </Link>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" asChild className="w-full">
                  <Link href={route('customers.invoices.index', customer.id)}>
                    View All Invoices
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Bandwidth Usage */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Bandwidth Usage</CardTitle>
                  <CardDescription>Current month usage statistics</CardDescription>
                </div>
                <Button variant="outline" asChild>
                  <Link href={`/bandwidth/usage?customer_id=${customer.id}`}>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Details
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                {loadingUsage ? (
                  <div className="text-center py-6 text-gray-500">
                    Loading usage data...
                  </div>
                ) : usageSummary ? (
                  <div className="space-y-4">
                    {/* Usage Summary Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <div className="flex items-center">
                          <Download className="h-5 w-5 text-blue-600 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-blue-600">Download</div>
                            <div className="text-lg font-bold text-blue-800">
                              {formatBytes(usageSummary.total_download)}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <div className="flex items-center">
                          <Upload className="h-5 w-5 text-green-600 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-green-600">Upload</div>
                            <div className="text-lg font-bold text-green-800">
                              {formatBytes(usageSummary.total_upload)}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <div className="flex items-center">
                          <Activity className="h-5 w-5 text-purple-600 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-purple-600">Total</div>
                            <div className="text-lg font-bold text-purple-800">
                              {formatBytes(usageSummary.total_bandwidth)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Recent Usage Table */}
                    {usageSummary.recent_usage.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Recent Usage</h4>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Period</TableHead>
                              <TableHead>Service</TableHead>
                              <TableHead>Download</TableHead>
                              <TableHead>Upload</TableHead>
                              <TableHead>Total</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {usageSummary.recent_usage.slice(0, 5).map((usage) => (
                              <TableRow key={usage.id}>
                                <TableCell className="text-sm">
                                  {formatDate(usage.period_start)}
                                </TableCell>
                                <TableCell className="text-sm">
                                  <Badge variant="outline" className="text-xs">
                                    {usage.service_type}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-sm">
                                  {formatBytes(usage.download)}
                                </TableCell>
                                <TableCell className="text-sm">
                                  {formatBytes(usage.upload)}
                                </TableCell>
                                <TableCell className="text-sm font-medium">
                                  {formatBytes(usage.total)}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    No usage data available for this customer.
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" asChild className="w-full">
                  <Link href={`/bandwidth/usage?customer_id=${customer.id}`}>
                    View Detailed Usage Report
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {customer.status === 'active' ? (
                  <Button onClick={handleSuspend} variant="outline" className="w-full text-orange-600 border-orange-600 hover:bg-orange-50">
                    <UserX className="h-4 w-4 mr-2" />
                    Suspend Customer
                  </Button>
                ) : (
                  <Button onClick={handleActivate} variant="outline" className="w-full text-green-600 border-green-600 hover:bg-green-50">
                    <UserCheck className="h-4 w-4 mr-2" />
                    Activate Customer
                  </Button>
                )}
                <Button asChild className="w-full">
                  <Link href={route('customers.subscriptions.create', customer.id)}>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Add Subscription
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href={route('customers.invoices.create', customer.id)}>
                    <FileText className="h-4 w-4 mr-2" />
                    Create Invoice
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href={`/bandwidth/usage?customer_id=${customer.id}`}>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Usage Report
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
