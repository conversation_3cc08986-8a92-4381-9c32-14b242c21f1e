import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { ArrowLeft, Save, User } from 'lucide-react';

interface Customer {
  id: number;
  name: string;
  email: string;
}

interface Subscription {
  id: number;
  customer_id: number;
  name: string;
  description: string | null;
  price: string;
  billing_cycle: string;
  start_date: string;
  end_date: string | null;
  status: string;
  customer: Customer;
}

interface EditSubscriptionProps {
  subscription: Subscription;
}

export default function EditSubscription({ subscription }: EditSubscriptionProps) {
  const { data, setData, put, processing, errors } = useForm({
    name: subscription.name || '',
    description: subscription.description || '',
    price: subscription.price || '',
    billing_cycle: subscription.billing_cycle || 'monthly',
    start_date: subscription.start_date || '',
    end_date: subscription.end_date || '',
    status: subscription.status || 'active',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(route('subscriptions.update', subscription.id));
  };

  return (
    <AppLayout>
      <Head title={`Edit Subscription: ${subscription.name}`} />
      
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('subscriptions.show', subscription.id)}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Subscription
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Edit Subscription</h1>
          </div>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer Information</CardTitle>
                  <CardDescription>Subscription is assigned to this customer</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center p-4 border rounded-md">
                    <User className="h-10 w-10 text-gray-400 mr-4" />
                    <div>
                      <h3 className="font-medium">{subscription.customer.name}</h3>
                      <p className="text-sm text-gray-500">{subscription.customer.email}</p>
                    </div>
                    <Button variant="outline" asChild className="ml-auto">
                      <Link href={route('customers.show', subscription.customer.id)}>
                        View Customer
                      </Link>
                    </Button>
                  </div>
                  <p className="text-sm text-gray-500">
                    Note: To change the customer, you need to create a new subscription.
                  </p>
                </CardContent>
              </Card>
              
              {/* Subscription Details */}
              <Card>
                <CardHeader>
                  <CardTitle>Subscription Details</CardTitle>
                  <CardDescription>Update the subscription details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name <span className="text-red-500">*</span></Label>
                    <Input 
                      id="name" 
                      value={data.name} 
                      onChange={e => setData('name', e.target.value)}
                      placeholder="Subscription name"
                    />
                    {errors.name && <p className="text-red-500 text-sm">{errors.name}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea 
                      id="description" 
                      value={data.description} 
                      onChange={e => setData('description', e.target.value)}
                      placeholder="Subscription description"
                      rows={3}
                    />
                    {errors.description && <p className="text-red-500 text-sm">{errors.description}</p>}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="space-y-6">
              {/* Billing Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Billing Information</CardTitle>
                  <CardDescription>Update the billing details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Price <span className="text-red-500">*</span></Label>
                    <div className="relative">
                      <span className="absolute left-3 top-2.5">$</span>
                      <Input 
                        id="price" 
                        type="number" 
                        step="0.01" 
                        min="0" 
                        value={data.price} 
                        onChange={e => setData('price', e.target.value)}
                        placeholder="0.00"
                        className="pl-7"
                      />
                    </div>
                    {errors.price && <p className="text-red-500 text-sm">{errors.price}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="billing_cycle">Billing Cycle <span className="text-red-500">*</span></Label>
                    <Select 
                      value={data.billing_cycle} 
                      onValueChange={value => setData('billing_cycle', value)}
                    >
                      <SelectTrigger id="billing_cycle">
                        <SelectValue placeholder="Select billing cycle" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="biannually">Biannually</SelectItem>
                        <SelectItem value="annually">Annually</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.billing_cycle && <p className="text-red-500 text-sm">{errors.billing_cycle}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="start_date">Start Date <span className="text-red-500">*</span></Label>
                    <Input 
                      id="start_date" 
                      type="date" 
                      value={data.start_date} 
                      onChange={e => setData('start_date', e.target.value)}
                    />
                    {errors.start_date && <p className="text-red-500 text-sm">{errors.start_date}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="end_date">End Date</Label>
                    <Input 
                      id="end_date" 
                      type="date" 
                      value={data.end_date} 
                      onChange={e => setData('end_date', e.target.value)}
                    />
                    <p className="text-sm text-gray-500">Leave empty for ongoing subscriptions</p>
                    {errors.end_date && <p className="text-red-500 text-sm">{errors.end_date}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select 
                      value={data.status} 
                      onValueChange={value => setData('status', value)}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                        <SelectItem value="expired">Expired</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.status && <p className="text-red-500 text-sm">{errors.status}</p>}
                  </div>
                </CardContent>
              </Card>
              
              {/* Submit Button */}
              <Card>
                <CardContent className="pt-6">
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={processing}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Update Subscription
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
