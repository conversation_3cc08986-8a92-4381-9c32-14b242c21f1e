import React, { useState } from 'react';
import { Head, Link, router, useForm } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowLeft,
  Edit,
  Trash,
  User,
  Calendar,
  CreditCard,
  Clock,
  Download,
  Mail,
  Plus,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText
} from 'lucide-react';
import { formatDate, formatCurrency } from '@/lib/utils';

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string | null;
  full_address: string;
}

interface Subscription {
  id: number;
  name: string;
  billing_cycle: string;
}

interface InvoiceItem {
  id: number;
  description: string;
  quantity: number;
  unit_price: string;
  total: string;
}

interface Payment {
  id: number;
  payment_method: string;
  amount: string;
  reference_number: string | null;
  transaction_id: string | null;
  status: string;
  payment_date: string | null;
  phone_number: string | null;
  mpesa_receipt_number: string | null;
  notes: string | null;
  confirmed_by: {
    id: number;
    name: string;
  } | null;
  created_at: string;
}

interface Invoice {
  id: number;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  paid_date: string | null;
  amount: string;
  tax_amount: string;
  total_amount: string;
  total_paid: number;
  remaining_balance: number;
  status: string;
  notes: string | null;
  customer: Customer;
  subscription: Subscription | null;
  items: InvoiceItem[];
  payments: Payment[];
  created_at: string;
  updated_at: string;
}

interface InvoiceShowProps {
  invoice: Invoice;
}

export default function InvoiceShow({ invoice }: InvoiceShowProps) {
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  const { data, setData, post, processing, errors, reset } = useForm({
    invoice_id: invoice.id,
    payment_method: 'cash',
    amount: invoice.remaining_balance,
    reference_number: '',
    transaction_id: '',
    phone_number: '',
    payment_date: new Date().toISOString().split('T')[0],
    notes: '',
  });

  // Update amount when payment method changes
  React.useEffect(() => {
    if (data.payment_method === 'cash') {
      // For cash payments, amount must equal remaining balance
      setData('amount', invoice.remaining_balance);
    }
  }, [data.payment_method, invoice.remaining_balance]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'overdue':
        return <Badge className="bg-red-500">Overdue</Badge>;
      case 'draft':
        return <Badge className="bg-gray-400">Draft</Badge>;
      case 'cancelled':
        return <Badge className="bg-gray-500">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500"><AlertCircle className="h-3 w-3 mr-1" />Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-500"><XCircle className="h-3 w-3 mr-1" />Failed</Badge>;
      case 'refunded':
        return <Badge className="bg-gray-500">Refunded</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this invoice?')) {
      router.delete(route('invoices.destroy', invoice.id));
    }
  };

  const handleStatusChange = (newStatus: string) => {
    if (confirm(`Are you sure you want to change the status to ${newStatus}?`)) {
      router.put(route('invoices.update', invoice.id), {
        status: newStatus,
        paid_date: newStatus === 'paid' ? new Date().toISOString().split('T')[0] : null
      });
    }
  };

  const handlePaymentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('payments.store'), {
      onSuccess: () => {
        reset();
        setShowPaymentForm(false);
      },
    });
  };

  return (
    <AppLayout>
      <Head title={`Invoice: ${invoice.invoice_number}`} />

      <div className="container mx-auto p-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <Button variant="ghost" asChild className="mr-2">
              <Link href={route('invoices.index')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoices
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">Invoice #{invoice.invoice_number}</h1>
            <div className="ml-4">{getStatusBadge(invoice.status)}</div>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" asChild>
              <Link href={route('invoices.edit', invoice.id)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            {/* Invoice Details */}
            <Card>
              <CardHeader>
                <CardTitle>Invoice Details</CardTitle>
                <CardDescription>Basic information about the invoice</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <User className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Customer</div>
                      <div>
                        <Link href={route('customers.show', invoice.customer.id)} className="hover:underline">
                          {invoice.customer.name}
                        </Link>
                      </div>
                      <div className="text-sm text-gray-500">{invoice.customer.email}</div>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CreditCard className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Subscription</div>
                      <div>
                        {invoice.subscription ? (
                          <Link href={route('subscriptions.show', invoice.subscription.id)} className="hover:underline">
                            {invoice.subscription.name}
                          </Link>
                        ) : (
                          <span className="text-gray-500">No subscription</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Issue Date</div>
                      <div>{formatDate(invoice.issue_date)}</div>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Clock className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                    <div>
                      <div className="text-sm font-medium text-gray-500">Due Date</div>
                      <div>{formatDate(invoice.due_date)}</div>
                    </div>
                  </div>
                  {invoice.paid_date && (
                    <div className="flex items-start">
                      <CreditCard className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
                      <div>
                        <div className="text-sm font-medium text-gray-500">Paid Date</div>
                        <div>{formatDate(invoice.paid_date)}</div>
                      </div>
                    </div>
                  )}
                </div>

                {invoice.notes && (
                  <div>
                    <div className="text-sm font-medium text-gray-500">Notes</div>
                    <div className="mt-1 p-3 bg-gray-50 rounded-md">{invoice.notes}</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Invoice Items */}
            <Card>
              <CardHeader>
                <CardTitle>Invoice Items</CardTitle>
                <CardDescription>Items included in this invoice</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Quantity</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invoice.items.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.description}</TableCell>
                        <TableCell className="text-right">{item.quantity}</TableCell>
                        <TableCell className="text-right">{formatCurrency(item.unit_price)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(item.total)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                <div className="mt-6 space-y-2">
                  <div className="flex justify-between border-t pt-2">
                    <span className="font-medium">Subtotal:</span>
                    <span>{formatCurrency(invoice.amount)}</span>
                  </div>
                  {parseFloat(invoice.tax_amount) > 0 && (
                    <div className="flex justify-between">
                      <span className="font-medium">Tax:</span>
                      <span>{formatCurrency(invoice.tax_amount)}</span>
                    </div>
                  )}
                  <div className="flex justify-between border-t pt-2 text-lg font-bold">
                    <span>Total:</span>
                    <span>{formatCurrency(invoice.total_amount)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment History */}
            {invoice.payments && invoice.payments.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Payment History</CardTitle>
                  <CardDescription>All payments made for this invoice</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Method</TableHead>
                        <TableHead>Reference</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Confirmed By</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invoice.payments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>
                            {payment.payment_date ? formatDate(payment.payment_date) : 'N/A'}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="capitalize">
                              {payment.payment_method}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {payment.payment_method === 'mpesa'
                              ? payment.mpesa_receipt_number || payment.transaction_id
                              : payment.reference_number || 'N/A'
                            }
                          </TableCell>
                          <TableCell className="font-medium">
                            {formatCurrency(payment.amount)}
                          </TableCell>
                          <TableCell>
                            {getPaymentStatusBadge(payment.status)}
                          </TableCell>
                          <TableCell>
                            {payment.confirmed_by ? payment.confirmed_by.name : 'System'}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="space-y-6">
            {/* Payment Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Summary</CardTitle>
                <CardDescription>Invoice payment status and balance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm font-medium text-gray-500">Total Amount</div>
                    <div className="text-lg font-semibold">{formatCurrency(invoice.total_amount)}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-500">Amount Paid</div>
                    <div className="text-lg font-semibold text-green-600">{formatCurrency(invoice.total_paid)}</div>
                  </div>
                </div>
                <div className="border-t pt-4">
                  <div className="text-sm font-medium text-gray-500">Remaining Balance</div>
                  <div className="text-xl font-bold text-red-600">{formatCurrency(invoice.remaining_balance)}</div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Actions */}
            {invoice.remaining_balance > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Record Payment</CardTitle>
                  <CardDescription>Add a new payment for this invoice</CardDescription>
                </CardHeader>
                <CardContent>
                  {!showPaymentForm ? (
                    <Button
                      onClick={() => setShowPaymentForm(true)}
                      className="w-full"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Payment
                    </Button>
                  ) : (
                    <form onSubmit={handlePaymentSubmit} className="space-y-4">
                      <div>
                        <Label htmlFor="payment_method">Payment Method</Label>
                        <Select value={data.payment_method} onValueChange={(value) => setData('payment_method', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment method" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cash">Cash</SelectItem>
                            <SelectItem value="mpesa">M-Pesa</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.payment_method && <div className="text-red-500 text-sm mt-1">{errors.payment_method}</div>}
                      </div>

                      <div>
                        <Label htmlFor="amount">Amount</Label>
                        <Input
                          id="amount"
                          type="number"
                          step="0.01"
                          min="0.01"
                          max={invoice.remaining_balance}
                          value={data.amount}
                          onChange={(e) => setData('amount', parseFloat(e.target.value))}
                          placeholder="Enter payment amount"
                          disabled={data.payment_method === 'cash'}
                        />
                        {data.payment_method === 'cash' && (
                          <div className="text-sm text-gray-600 mt-1">
                            Cash payments must equal the full remaining balance. Partial payments are not supported.
                          </div>
                        )}
                        {errors.amount && <div className="text-red-500 text-sm mt-1">{errors.amount}</div>}
                      </div>

                      {data.payment_method === 'mpesa' && (
                        <>
                          <div>
                            <Label htmlFor="phone_number">Phone Number</Label>
                            <Input
                              id="phone_number"
                              type="text"
                              value={data.phone_number}
                              onChange={(e) => setData('phone_number', e.target.value)}
                              placeholder="254XXXXXXXXX"
                            />
                            {errors.phone_number && <div className="text-red-500 text-sm mt-1">{errors.phone_number}</div>}
                          </div>

                          <div>
                            <Label htmlFor="transaction_id">M-Pesa Transaction ID</Label>
                            <Input
                              id="transaction_id"
                              type="text"
                              value={data.transaction_id}
                              onChange={(e) => setData('transaction_id', e.target.value)}
                              placeholder="Enter M-Pesa transaction ID"
                            />
                            {errors.transaction_id && <div className="text-red-500 text-sm mt-1">{errors.transaction_id}</div>}
                          </div>
                        </>
                      )}

                      {data.payment_method === 'cash' && (
                        <div>
                          <Label htmlFor="reference_number">Reference Number</Label>
                          <Input
                            id="reference_number"
                            type="text"
                            value={data.reference_number}
                            onChange={(e) => setData('reference_number', e.target.value)}
                            placeholder="Enter reference number (optional)"
                          />
                          {errors.reference_number && <div className="text-red-500 text-sm mt-1">{errors.reference_number}</div>}
                        </div>
                      )}

                      <div>
                        <Label htmlFor="payment_date">Payment Date</Label>
                        <Input
                          id="payment_date"
                          type="date"
                          value={data.payment_date}
                          onChange={(e) => setData('payment_date', e.target.value)}
                        />
                        {errors.payment_date && <div className="text-red-500 text-sm mt-1">{errors.payment_date}</div>}
                      </div>

                      <div>
                        <Label htmlFor="notes">Notes (Optional)</Label>
                        <Textarea
                          id="notes"
                          value={data.notes}
                          onChange={(e) => setData('notes', e.target.value)}
                          placeholder="Add any notes about this payment"
                          rows={3}
                        />
                        {errors.notes && <div className="text-red-500 text-sm mt-1">{errors.notes}</div>}
                      </div>

                      <div className="flex space-x-2">
                        <Button type="submit" disabled={processing} className="flex-1">
                          {processing ? 'Processing...' : 'Record Payment'}
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setShowPaymentForm(false);
                            reset();
                          }}
                          className="flex-1"
                        >
                          Cancel
                        </Button>
                      </div>
                    </form>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Status Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Status Actions</CardTitle>
                <CardDescription>Manage invoice status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {invoice.status !== 'paid' && (
                  <Button
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={() => handleStatusChange('paid')}
                  >
                    Mark as Paid
                  </Button>
                )}

                {invoice.status !== 'pending' && (
                  <Button
                    className="w-full bg-yellow-600 hover:bg-yellow-700"
                    onClick={() => handleStatusChange('pending')}
                  >
                    Mark as Pending
                  </Button>
                )}

                {invoice.status !== 'cancelled' && (
                  <Button
                    className="w-full bg-red-600 hover:bg-red-700"
                    onClick={() => handleStatusChange('cancelled')}
                  >
                    Cancel Invoice
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button asChild className="w-full">
                  <Link href={route('invoices.view-pdf', invoice.id)} target="_blank">
                    <FileText className="h-4 w-4 mr-2" />
                    View PDF
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href={route('invoices.download', invoice.id)}>
                    <Download className="h-4 w-4 mr-2" />
                    Download PDF
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href={`/invoices/${invoice.id}/send`}>
                    <Mail className="h-4 w-4 mr-2" />
                    Send to Customer
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href={route('customers.show', invoice.customer.id)}>
                    <User className="h-4 w-4 mr-2" />
                    View Customer
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Invoice Info */}
            <Card>
              <CardHeader>
                <CardTitle>Invoice Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm font-medium text-gray-500">Created</div>
                  <div>{formatDate(invoice.created_at)}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500">Last Updated</div>
                  <div>{formatDate(invoice.updated_at)}</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
