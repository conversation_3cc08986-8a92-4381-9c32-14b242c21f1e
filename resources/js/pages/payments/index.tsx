import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DollarSign,
  TrendingUp,
  Target,
  AlertTriangle,
  CreditCard,
  Smartphone,
  Calendar,
  Download,
  Filter,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

interface FinancialOverview {
  total_monthly_revenue: number;
  expected_monthly_revenue: number;
  achievement_percentage: number;
  outstanding_payments: number;
}

interface PaymentMethod {
  method: string;
  count: number;
  total: number;
  percentage: number;
  average: number;
}

interface PaymentTrend {
  month: string;
  revenue: number;
  payments_count: number;
}

interface RecentPayment {
  id: number;
  amount: number;
  payment_method: string;
  status: string;
  payment_date: string | null;
  customer_name: string;
  invoice_number: string;
  confirmed_by: string;
}

interface StatusBreakdown {
  status: string;
  count: number;
  total: number;
}

interface StatusOverview {
  status_breakdown: StatusBreakdown[];
  success_rate: number;
  failed_mpesa_count: number;
}

interface PaymentsDashboardProps {
  overview: FinancialOverview;
  paymentMethods: PaymentMethod[];
  trends: PaymentTrend[];
  recentPayments: RecentPayment[];
  statusOverview: StatusOverview;
  filters: {
    date_range?: string;
    from_date?: string;
    to_date?: string;
  };
}

export default function PaymentsDashboard({
  overview,
  paymentMethods,
  trends,
  recentPayments,
  statusOverview,
  filters
}: PaymentsDashboardProps) {
  const [dateRange, setDateRange] = useState(filters.date_range || 'this_month');
  const [fromDate, setFromDate] = useState(filters.from_date || '');
  const [toDate, setToDate] = useState(filters.to_date || '');

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      case 'failed':
        return <Badge className="bg-red-500"><XCircle className="h-3 w-3 mr-1" />Failed</Badge>;
      default:
        return <Badge className="bg-gray-500">{status}</Badge>;
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'mpesa':
        return <Smartphone className="h-4 w-4" />;
      case 'cash':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const handleFilterChange = () => {
    const params: any = { date_range: dateRange };
    if (dateRange === 'custom') {
      params.from_date = fromDate;
      params.to_date = toDate;
    }
    router.get(route('payments.index'), params, { preserveState: true });
  };

  const exportData = () => {
    const params = new URLSearchParams();
    params.append('date_range', dateRange);
    if (dateRange === 'custom') {
      params.append('from_date', fromDate);
      params.append('to_date', toDate);
    }

    // Create a temporary link to trigger download
    const url = route('payments.export') + '?' + params.toString();
    window.open(url, '_blank');
  };

  return (
    <AppLayout>
      <Head title="Payments Dashboard" />

      <div className="container mx-auto p-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Payments Dashboard</h1>
            <p className="text-gray-600 mt-2">Comprehensive payment analytics and reporting</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={exportData}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="date_range">Date Range</Label>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select date range" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="this_week">This Week</SelectItem>
                    <SelectItem value="this_month">This Month</SelectItem>
                    <SelectItem value="last_month">Last Month</SelectItem>
                    <SelectItem value="custom">Custom Range</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {dateRange === 'custom' && (
                <>
                  <div>
                    <Label htmlFor="from_date">From Date</Label>
                    <Input
                      id="from_date"
                      type="date"
                      value={fromDate}
                      onChange={(e) => setFromDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="to_date">To Date</Label>
                    <Input
                      id="to_date"
                      type="date"
                      value={toDate}
                      onChange={(e) => setToDate(e.target.value)}
                    />
                  </div>
                </>
              )}

              <div className="flex items-end">
                <Button onClick={handleFilterChange}>
                  Apply Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Financial Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(overview.total_monthly_revenue)}</div>
              <p className="text-xs text-muted-foreground">Current month total</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Expected Revenue</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(overview.expected_monthly_revenue)}</div>
              <p className="text-xs text-muted-foreground">Based on active subscriptions</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Achievement Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.achievement_percentage}%</div>
              <p className="text-xs text-muted-foreground">Revenue vs expected</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(overview.outstanding_payments)}</div>
              <p className="text-xs text-muted-foreground">Pending payments</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Payment Methods Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>Current month breakdown by payment method</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentMethods.map((method) => (
                  <div key={method.method} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getPaymentMethodIcon(method.method)}
                      <span className="capitalize font-medium">{method.method}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(method.total)}</div>
                      <div className="text-sm text-gray-500">
                        {method.count} payments • {method.percentage}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Status Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Status</CardTitle>
              <CardDescription>Success rate and status breakdown</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{statusOverview.success_rate}%</div>
                  <div className="text-sm text-gray-500">Success Rate</div>
                </div>

                <div className="space-y-2">
                  {statusOverview.status_breakdown.map((status) => (
                    <div key={status.status} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(status.status)}
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{formatCurrency(status.total)}</div>
                        <div className="text-sm text-gray-500">{status.count} payments</div>
                      </div>
                    </div>
                  ))}
                </div>

                {statusOverview.failed_mpesa_count > 0 && (
                  <div className="mt-4 p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center text-red-700">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      <span className="text-sm font-medium">
                        {statusOverview.failed_mpesa_count} failed M-Pesa payments need attention
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Revenue Trends Chart */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Revenue Trends</CardTitle>
            <CardDescription>Monthly revenue over the last 12 months</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Simple bar chart representation */}
              <div className="grid grid-cols-12 gap-2 h-40">
                {trends.map((trend, index) => {
                  const maxRevenue = Math.max(...trends.map(t => t.revenue));
                  const height = maxRevenue > 0 ? (trend.revenue / maxRevenue) * 100 : 0;

                  return (
                    <div key={index} className="flex flex-col items-center justify-end">
                      <div
                        className="bg-blue-500 w-full rounded-t"
                        style={{ height: `${height}%` }}
                        title={`${trend.month}: ${formatCurrency(trend.revenue)}`}
                      />
                      <div className="text-xs mt-1 text-center transform -rotate-45 origin-top-left">
                        {trend.month.split(' ')[0]}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Trend summary */}
              <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-lg font-semibold">
                    {formatCurrency(trends.reduce((sum, t) => sum + t.revenue, 0))}
                  </div>
                  <div className="text-sm text-gray-500">Total (12 months)</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">
                    {formatCurrency(trends.reduce((sum, t) => sum + t.revenue, 0) / trends.length)}
                  </div>
                  <div className="text-sm text-gray-500">Average per month</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-semibold">
                    {trends.reduce((sum, t) => sum + t.payments_count, 0)}
                  </div>
                  <div className="text-sm text-gray-500">Total payments</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Payments */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Payments</CardTitle>
            <CardDescription>Latest payment transactions</CardDescription>
          </CardHeader>
          <CardContent>
            {recentPayments.length === 0 ? (
              <div className="text-center py-8">
                <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No payments found</h3>
                <p className="text-gray-500">No payment transactions for the selected period.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Confirmed By</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentPayments.map((payment) => (
                    <TableRow key={payment.id}>
                      <TableCell className="font-medium">
                        {payment.customer_name}
                      </TableCell>
                      <TableCell>
                        <Link
                          href={route('invoices.show', payment.invoice_number.split('-').pop())}
                          className="text-blue-600 hover:underline"
                        >
                          {payment.invoice_number}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getPaymentMethodIcon(payment.payment_method)}
                          <span className="capitalize">{payment.payment_method}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {formatCurrency(payment.amount)}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(payment.status)}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          {payment.payment_date ? formatDate(payment.payment_date) : 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>
                        {payment.confirmed_by}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={route('payments.show', payment.id)}>
                            View
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
