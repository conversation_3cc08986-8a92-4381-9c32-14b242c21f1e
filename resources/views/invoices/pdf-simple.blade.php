<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .invoice-title {
            font-size: 20px;
            margin-bottom: 20px;
        }
        .section {
            margin-bottom: 20px;
        }
        .row {
            margin-bottom: 5px;
        }
        .label {
            font-weight: bold;
            display: inline-block;
            width: 120px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ $companyInfo['name'] ?? 'ISP Management System' }}</div>
        <div class="invoice-title">INVOICE #{{ $invoice->invoice_number }}</div>
    </div>

    <div class="section">
        <h3>Bill To:</h3>
        <div class="row">
            <span class="label">Customer:</span>
            {{ $invoice->customer->name }}
        </div>
        <div class="row">
            <span class="label">Email:</span>
            {{ $invoice->customer->email }}
        </div>
        @if($invoice->customer->phone)
        <div class="row">
            <span class="label">Phone:</span>
            {{ $invoice->customer->phone }}
        </div>
        @endif
    </div>

    <div class="section">
        <h3>Invoice Details:</h3>
        <div class="row">
            <span class="label">Issue Date:</span>
            {{ $invoice->issue_date->format('M d, Y') }}
        </div>
        <div class="row">
            <span class="label">Due Date:</span>
            {{ $invoice->due_date->format('M d, Y') }}
        </div>
        <div class="row">
            <span class="label">Status:</span>
            {{ ucfirst($invoice->status) }}
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th>Description</th>
                <th class="text-right">Quantity</th>
                <th class="text-right">Unit Price</th>
                <th class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->items as $item)
            <tr>
                <td>{{ $item->description }}</td>
                <td class="text-right">{{ $item->quantity }}</td>
                <td class="text-right">{{ number_format($item->unit_price, 2) }}</td>
                <td class="text-right">{{ number_format($item->total_price, 2) }}</td>
            </tr>
            @endforeach
            <tr class="total-row">
                <td colspan="3">Total Amount:</td>
                <td class="text-right">{{ number_format($invoice->total_amount, 2) }}</td>
            </tr>
        </tbody>
    </table>

    @if($invoice->notes)
    <div class="section">
        <h3>Notes:</h3>
        <p>{{ $invoice->notes }}</p>
    </div>
    @endif

    <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #666;">
        Generated on {{ now()->format('M d, Y \a\t g:i A') }}
    </div>
</body>
</html>
