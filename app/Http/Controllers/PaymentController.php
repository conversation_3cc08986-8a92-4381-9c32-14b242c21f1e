<?php

namespace App\Http\Controllers;

use App\Billing\CashPaymentGateway;
use App\Billing\MpesaPaymentGateway;
use App\Contracts\PaymentGatewayInterface;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class PaymentController extends Controller
{
    /**
     * Display payments dashboard with comprehensive analytics.
     */
    public function index(Request $request)
    {
        // Check if this is a request for the payments list (with pagination)
        if ($request->has('list') || $request->has('page')) {
            return $this->paymentsList($request);
        }

        // Get dashboard data directly without caching
        $dashboardData = $this->getDashboardData($request);

        return Inertia::render('payments/index', $dashboardData);
    }

    /**
     * Display paginated payments list with filtering.
     */
    public function paymentsList(Request $request)
    {
        $query = Payment::with(['invoice.customer', 'invoice.subscription', 'confirmedBy'])
            ->select('payments.*');

        // Apply filters
        $this->applyPaymentFilters($query, $request);

        // Apply sorting
        $sortBy = $request->get('sort_by', 'payment_date');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        // Paginate results
        $payments = $query->paginate(25)->withQueryString();

        // Transform payment data for frontend
        $payments->getCollection()->transform(function ($payment) {
            return [
                'id' => $payment->id,
                'amount' => $payment->amount,
                'payment_method' => $payment->payment_method,
                'status' => $payment->status,
                'payment_date' => $payment->payment_date?->format('Y-m-d H:i:s'),
                'reference_number' => $payment->reference_number,
                'transaction_id' => $payment->transaction_id,
                'phone_number' => $payment->phone_number,
                'notes' => $payment->notes,
                'customer' => [
                    'id' => $payment->invoice->customer->id,
                    'name' => $payment->invoice->customer->name,
                    'email' => $payment->invoice->customer->email,
                ],
                'invoice' => [
                    'id' => $payment->invoice->id,
                    'invoice_number' => $payment->invoice->invoice_number,
                    'total_amount' => $payment->invoice->total_amount,
                ],
                'confirmed_by_user' => $payment->confirmedBy?->name,
                'created_at' => $payment->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return Inertia::render('payments/list', [
            'payments' => $payments,
            'filters' => $request->only(['search', 'payment_method', 'status', 'date_from', 'date_to', 'customer_id', 'month']),
            'analytics' => $this->getPaymentAnalytics($request),
        ]);
    }

    /**
     * Apply filters to payment query.
     */
    private function applyPaymentFilters($query, Request $request)
    {
        // Search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('invoice.customer', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('reference_number', 'like', "%{$search}%")
              ->orWhere('transaction_id', 'like', "%{$search}%");
        }

        // Payment method filter
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Customer filter
        if ($request->filled('customer_id')) {
            $query->whereHas('invoice', function ($q) use ($request) {
                $q->where('customer_id', $request->get('customer_id'));
            });
        }

        // Month filter (default to current month)
        $month = $request->get('month', now()->format('Y-m'));
        if ($month) {
            $startOfMonth = Carbon::createFromFormat('Y-m', $month)->startOfMonth();
            $endOfMonth = $startOfMonth->copy()->endOfMonth();
            $query->whereBetween('payment_date', [$startOfMonth, $endOfMonth]);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->where('payment_date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('payment_date', '<=', $request->get('date_to'));
        }
    }

    /**
     * Get payment analytics for the list view.
     */
    private function getPaymentAnalytics(Request $request)
    {
        $query = Payment::query();
        $this->applyPaymentFilters($query, $request);

        $totalPayments = $query->count();
        $totalAmount = $query->sum('amount');

        // Payment method breakdown
        $methodBreakdown = Payment::query()
            ->selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total')
            ->tap(function ($q) use ($request) {
                $this->applyPaymentFilters($q, $request);
            })
            ->groupBy('payment_method')
            ->get()
            ->map(function ($item) use ($totalAmount) {
                return [
                    'method' => $item->payment_method,
                    'count' => $item->count,
                    'total' => $item->total,
                    'percentage' => $totalAmount > 0 ? round(($item->total / $totalAmount) * 100, 1) : 0,
                ];
            });

        // Status breakdown
        $statusBreakdown = Payment::query()
            ->selectRaw('status, COUNT(*) as count, SUM(amount) as total')
            ->tap(function ($q) use ($request) {
                $this->applyPaymentFilters($q, $request);
            })
            ->groupBy('status')
            ->get();

        return [
            'total_payments' => $totalPayments,
            'total_amount' => $totalAmount,
            'method_breakdown' => $methodBreakdown,
            'status_breakdown' => $statusBreakdown,
        ];
    }

    /**
     * Get comprehensive dashboard data.
     */
    private function getDashboardData(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'overview' => $this->getFinancialOverview($currentMonth),
            'paymentMethods' => $this->getPaymentMethodBreakdown($currentMonth),
            'trends' => $this->getPaymentTrends(),
            'recentPayments' => $this->getRecentPayments(),
            'statusOverview' => $this->getPaymentStatusOverview($currentMonth),
            'filters' => $request->only(['date_range', 'from_date', 'to_date']),
        ];
    }

    /**
     * Get financial overview cards data.
     */
    private function getFinancialOverview($currentMonth)
    {
        $currentMonthEnd = $currentMonth->copy()->endOfMonth();

        // Total monthly revenue (current month) - actual revenue
        $actualMonthlyRevenue = Payment::completed()
            ->whereBetween('payment_date', [$currentMonth, $currentMonthEnd])
            ->sum('amount');

        // Expected monthly revenue (based on active subscriptions)
        $expectedMonthlyRevenue = Subscription::where('status', 'active')
            ->sum('price');

        // Revenue achievement percentage
        $achievementPercentage = $expectedMonthlyRevenue > 0 ?
            round(($actualMonthlyRevenue / $expectedMonthlyRevenue) * 100, 1) : 0;

        // Outstanding payments (unpaid invoices)
        $outstandingPayments = Invoice::where('status', '!=', 'paid')
            ->sum('total_amount');

        // Separate M-Pesa and Cash analytics for current month
        $mpesaAnalytics = Payment::completed()
            ->where('payment_method', 'mpesa')
            ->whereBetween('payment_date', [$currentMonth, $currentMonthEnd])
            ->selectRaw('COUNT(*) as count, SUM(amount) as total')
            ->first();

        $cashAnalytics = Payment::completed()
            ->where('payment_method', 'cash')
            ->whereBetween('payment_date', [$currentMonth, $currentMonthEnd])
            ->selectRaw('COUNT(*) as count, SUM(amount) as total')
            ->first();

        $totalMonthlyPayments = ($mpesaAnalytics->total ?? 0) + ($cashAnalytics->total ?? 0);

        $mpesaPercentage = $totalMonthlyPayments > 0 ?
            round((($mpesaAnalytics->total ?? 0) / $totalMonthlyPayments) * 100, 1) : 0;

        $cashPercentage = $totalMonthlyPayments > 0 ?
            round((($cashAnalytics->total ?? 0) / $totalMonthlyPayments) * 100, 1) : 0;

        return [
            'actual_monthly_revenue' => $actualMonthlyRevenue,
            'expected_monthly_revenue' => $expectedMonthlyRevenue,
            'achievement_percentage' => $achievementPercentage,
            'outstanding_payments' => max(0, $outstandingPayments),
            'mpesa_analytics' => [
                'count' => $mpesaAnalytics->count ?? 0,
                'total' => $mpesaAnalytics->total ?? 0,
                'percentage' => $mpesaPercentage,
            ],
            'cash_analytics' => [
                'count' => $cashAnalytics->count ?? 0,
                'total' => $cashAnalytics->total ?? 0,
                'percentage' => $cashPercentage,
            ],
            'total_monthly_payments' => $totalMonthlyPayments,
        ];
    }

    /**
     * Get payment method breakdown.
     */
    private function getPaymentMethodBreakdown($currentMonth)
    {
        $currentMonthEnd = $currentMonth->copy()->endOfMonth();

        $breakdown = Payment::completed()
            ->whereBetween('payment_date', [$currentMonth, $currentMonthEnd])
            ->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('payment_method')
            ->get();

        $totalAmount = $breakdown->sum('total');
        $totalCount = $breakdown->sum('count');

        return $breakdown->map(function ($item) use ($totalAmount) {
            return [
                'method' => $item->payment_method,
                'count' => $item->count,
                'total' => $item->total,
                'percentage' => $totalAmount > 0 ? round(($item->total / $totalAmount) * 100, 1) : 0,
                'average' => $item->count > 0 ? round($item->total / $item->count, 2) : 0,
            ];
        });
    }

    /**
     * Get payment trends for the last 12 months.
     */
    private function getPaymentTrends()
    {
        $trends = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();

            $revenue = Payment::completed()
                ->whereBetween('payment_date', [$monthStart, $monthEnd])
                ->sum('amount');

            $trends[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue,
                'payments_count' => Payment::completed()
                    ->whereBetween('payment_date', [$monthStart, $monthEnd])
                    ->count(),
            ];
        }

        return $trends;
    }

    /**
     * Get recent payments for quick overview.
     */
    private function getRecentPayments()
    {
        return Payment::with(['invoice.customer', 'confirmedBy'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'payment_method' => $payment->payment_method,
                    'status' => $payment->status,
                    'payment_date' => $payment->payment_date,
                    'customer_name' => $payment->invoice->customer->name,
                    'invoice_number' => $payment->invoice->invoice_number,
                    'confirmed_by' => $payment->confirmedBy?->name ?? 'System',
                ];
            });
    }

    /**
     * Get payment status overview.
     */
    private function getPaymentStatusOverview($currentMonth)
    {
        $currentMonthEnd = $currentMonth->copy()->endOfMonth();

        $statusCounts = Payment::whereBetween('created_at', [$currentMonth, $currentMonthEnd])
            ->select('status', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('status')
            ->get();

        $totalPayments = $statusCounts->sum('count');
        $successRate = $totalPayments > 0 ?
            round(($statusCounts->where('status', 'completed')->first()?->count ?? 0) / $totalPayments * 100, 1) : 0;

        return [
            'status_breakdown' => $statusCounts,
            'success_rate' => $successRate,
            'failed_mpesa_count' => Payment::where('payment_method', 'mpesa')
                ->where('status', 'failed')
                ->whereBetween('created_at', [$currentMonth, $currentMonthEnd])
                ->count(),
        ];
    }

    /**
     * Get date range from request.
     */
    private function getDateRange(Request $request)
    {
        $range = $request->input('date_range', 'this_month');

        switch ($range) {
            case 'today':
                return [Carbon::today(), Carbon::today()->endOfDay()];
            case 'this_week':
                return [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
            case 'last_month':
                return [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()];
            case 'custom':
                return [
                    Carbon::parse($request->input('from_date', Carbon::now()->startOfMonth())),
                    Carbon::parse($request->input('to_date', Carbon::now()->endOfMonth())),
                ];
            default: // this_month
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
        }
    }

    /**
     * Store a newly created payment.
     */
    public function store(Request $request)
    {
        $invoice = Invoice::findOrFail($request->invoice_id);

        // Build validation rules dynamically based on payment method and invoice
        $rules = [
            'invoice_id' => 'required|exists:invoices,id',
            'payment_method' => 'required|in:cash,mpesa',
            'reference_number' => 'nullable|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'payment_date' => 'nullable|date|before_or_equal:today',
            'notes' => 'nullable|string|max:1000',
        ];

        // For cash payments, amount must equal remaining balance exactly
        if ($request->payment_method === 'cash') {
            $rules['amount'] = [
                'required',
                'numeric',
                'min:0.01',
                function ($attribute, $value, $fail) use ($invoice) {
                    if (abs($value - $invoice->remaining_balance) > 0.01) {
                        $fail('Payment amount must equal the full remaining balance of '.number_format($invoice->remaining_balance, 2).'. Partial payments are not supported.');
                    }
                },
            ];
        } else {
            // For other payment methods, allow any amount up to remaining balance
            $rules['amount'] = 'required|numeric|min:0.01|max:'.$invoice->remaining_balance;
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check if invoice is already paid
        if ($invoice->status === 'paid') {
            return back()->withErrors(['invoice' => 'This invoice is already paid.'])->withInput();
        }

        // Get the appropriate payment gateway
        $gateway = $this->getPaymentGateway($request->payment_method);

        // Process the payment
        $result = $gateway->processPayment($invoice, $request->all());

        if ($result['success']) {
            return redirect()->route('invoices.show', $invoice->id)
                ->with('success', $result['message']);
        } else {
            $errors = isset($result['errors']) ? $result['errors'] : [$result['message']];

            return back()->withErrors(['payment' => $errors])->withInput();
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $payment->load(['invoice.customer', 'confirmedBy']);

        return Inertia::render('payments/show', [
            'payment' => $payment,
        ]);
    }

    /**
     * Confirm a pending cash payment.
     */
    public function confirm(Request $request, Payment $payment)
    {
        if ($payment->payment_method !== 'cash') {
            return back()->withErrors(['payment' => 'Only cash payments can be manually confirmed.']);
        }

        if ($payment->status !== 'pending') {
            return back()->withErrors(['payment' => 'Only pending payments can be confirmed.']);
        }

        $gateway = new CashPaymentGateway;
        $result = $gateway->confirmPayment($payment, Auth::id());

        if ($result['success']) {
            return back()->with('success', $result['message']);
        } else {
            return back()->withErrors(['payment' => $result['message']]);
        }
    }

    /**
     * Get pending cash payments for admin confirmation.
     */
    public function pendingCashPayments()
    {
        $pendingPayments = Payment::with(['invoice.customer'])
            ->where('payment_method', 'cash')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return Inertia::render('payments/pending-cash', [
            'payments' => $pendingPayments,
        ]);
    }

    /**
     * Get the appropriate payment gateway based on payment method.
     */
    private function getPaymentGateway(string $paymentMethod): PaymentGatewayInterface
    {
        return match ($paymentMethod) {
            'cash' => new CashPaymentGateway,
            'mpesa' => new MpesaPaymentGateway,
            default => throw new \InvalidArgumentException("Unsupported payment method: {$paymentMethod}"),
        };
    }

    /**
     * Export payments data as CSV.
     */
    public function export(Request $request)
    {
        $dateRange = $this->getDateRange($request);

        $payments = Payment::with(['invoice.customer', 'confirmedBy'])
            ->whereBetween('payment_date', $dateRange)
            ->orderBy('payment_date', 'desc')
            ->get();

        $filename = 'payments_'.Carbon::now()->format('Y-m-d_H-i-s').'.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($payments) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Payment ID',
                'Customer Name',
                'Customer Email',
                'Invoice Number',
                'Payment Method',
                'Amount',
                'Status',
                'Payment Date',
                'Transaction ID',
                'Reference Number',
                'Confirmed By',
                'Notes',
                'Created At',
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->invoice->customer->name,
                    $payment->invoice->customer->email,
                    $payment->invoice->invoice_number,
                    ucfirst($payment->payment_method),
                    $payment->amount,
                    ucfirst($payment->status),
                    $payment->payment_date ? $payment->payment_date->format('Y-m-d H:i:s') : '',
                    $payment->transaction_id ?? '',
                    $payment->reference_number ?? '',
                    $payment->confirmedBy?->name ?? 'System',
                    $payment->notes ?? '',
                    $payment->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
