<?php

namespace App\Http\Controllers;

use App\Billing\CashPaymentGateway;
use App\Billing\MpesaPaymentGateway;
use App\Contracts\PaymentGatewayInterface;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\Subscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class PaymentController extends Controller
{
    /**
     * Display payments dashboard with comprehensive analytics.
     */
    public function index(Request $request)
    {
        // Get dashboard data directly without caching
        $dashboardData = $this->getDashboardData($request);

        return Inertia::render('payments/index', $dashboardData);
    }

    /**
     * Get comprehensive dashboard data.
     */
    private function getDashboardData(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'overview' => $this->getFinancialOverview($currentMonth),
            'paymentMethods' => $this->getPaymentMethodBreakdown($currentMonth),
            'trends' => $this->getPaymentTrends(),
            'recentPayments' => $this->getRecentPayments(),
            'statusOverview' => $this->getPaymentStatusOverview($currentMonth),
            'filters' => $request->only(['date_range', 'from_date', 'to_date']),
        ];
    }

    /**
     * Get financial overview cards data.
     */
    private function getFinancialOverview($currentMonth)
    {
        $currentMonthEnd = $currentMonth->copy()->endOfMonth();

        // Total monthly revenue (current month)
        $totalMonthlyRevenue = Payment::completed()
            ->whereBetween('payment_date', [$currentMonth, $currentMonthEnd])
            ->sum('amount');

        // Expected monthly revenue (based on active subscriptions)
        $expectedRevenue = Subscription::where('status', 'active')
            ->sum('price');

        // Revenue achievement percentage
        $achievementPercentage = $expectedRevenue > 0 ? ($totalMonthlyRevenue / $expectedRevenue) * 100 : 0;

        // Outstanding payments
        $outstandingPayments = Invoice::where('status', '!=', 'paid')
            ->sum('total_amount') - Payment::completed()->sum('amount');

        return [
            'total_monthly_revenue' => $totalMonthlyRevenue,
            'expected_monthly_revenue' => $expectedRevenue,
            'achievement_percentage' => round($achievementPercentage, 1),
            'outstanding_payments' => max(0, $outstandingPayments),
        ];
    }

    /**
     * Get payment method breakdown.
     */
    private function getPaymentMethodBreakdown($currentMonth)
    {
        $currentMonthEnd = $currentMonth->copy()->endOfMonth();

        $breakdown = Payment::completed()
            ->whereBetween('payment_date', [$currentMonth, $currentMonthEnd])
            ->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('payment_method')
            ->get();

        $totalAmount = $breakdown->sum('total');
        $totalCount = $breakdown->sum('count');

        return $breakdown->map(function ($item) use ($totalAmount) {
            return [
                'method' => $item->payment_method,
                'count' => $item->count,
                'total' => $item->total,
                'percentage' => $totalAmount > 0 ? round(($item->total / $totalAmount) * 100, 1) : 0,
                'average' => $item->count > 0 ? round($item->total / $item->count, 2) : 0,
            ];
        });
    }

    /**
     * Get payment trends for the last 12 months.
     */
    private function getPaymentTrends()
    {
        $trends = [];

        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthStart = $month->copy()->startOfMonth();
            $monthEnd = $month->copy()->endOfMonth();

            $revenue = Payment::completed()
                ->whereBetween('payment_date', [$monthStart, $monthEnd])
                ->sum('amount');

            $trends[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue,
                'payments_count' => Payment::completed()
                    ->whereBetween('payment_date', [$monthStart, $monthEnd])
                    ->count(),
            ];
        }

        return $trends;
    }

    /**
     * Get recent payments for quick overview.
     */
    private function getRecentPayments()
    {
        return Payment::with(['invoice.customer', 'confirmedBy'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'amount' => $payment->amount,
                    'payment_method' => $payment->payment_method,
                    'status' => $payment->status,
                    'payment_date' => $payment->payment_date,
                    'customer_name' => $payment->invoice->customer->name,
                    'invoice_number' => $payment->invoice->invoice_number,
                    'confirmed_by' => $payment->confirmedBy?->name ?? 'System',
                ];
            });
    }

    /**
     * Get payment status overview.
     */
    private function getPaymentStatusOverview($currentMonth)
    {
        $currentMonthEnd = $currentMonth->copy()->endOfMonth();

        $statusCounts = Payment::whereBetween('created_at', [$currentMonth, $currentMonthEnd])
            ->select('status', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('status')
            ->get();

        $totalPayments = $statusCounts->sum('count');
        $successRate = $totalPayments > 0 ?
            round(($statusCounts->where('status', 'completed')->first()?->count ?? 0) / $totalPayments * 100, 1) : 0;

        return [
            'status_breakdown' => $statusCounts,
            'success_rate' => $successRate,
            'failed_mpesa_count' => Payment::where('payment_method', 'mpesa')
                ->where('status', 'failed')
                ->whereBetween('created_at', [$currentMonth, $currentMonthEnd])
                ->count(),
        ];
    }

    /**
     * Get date range from request.
     */
    private function getDateRange(Request $request)
    {
        $range = $request->input('date_range', 'this_month');

        switch ($range) {
            case 'today':
                return [Carbon::today(), Carbon::today()->endOfDay()];
            case 'this_week':
                return [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
            case 'last_month':
                return [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()];
            case 'custom':
                return [
                    Carbon::parse($request->input('from_date', Carbon::now()->startOfMonth())),
                    Carbon::parse($request->input('to_date', Carbon::now()->endOfMonth())),
                ];
            default: // this_month
                return [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
        }
    }

    /**
     * Store a newly created payment.
     */
    public function store(Request $request)
    {
        $invoice = Invoice::findOrFail($request->invoice_id);

        // Build validation rules dynamically based on payment method and invoice
        $rules = [
            'invoice_id' => 'required|exists:invoices,id',
            'payment_method' => 'required|in:cash,mpesa',
            'reference_number' => 'nullable|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'payment_date' => 'nullable|date|before_or_equal:today',
            'notes' => 'nullable|string|max:1000',
        ];

        // For cash payments, amount must equal remaining balance exactly
        if ($request->payment_method === 'cash') {
            $rules['amount'] = [
                'required',
                'numeric',
                'min:0.01',
                function ($attribute, $value, $fail) use ($invoice) {
                    if (abs($value - $invoice->remaining_balance) > 0.01) {
                        $fail('Payment amount must equal the full remaining balance of '.number_format($invoice->remaining_balance, 2).'. Partial payments are not supported.');
                    }
                },
            ];
        } else {
            // For other payment methods, allow any amount up to remaining balance
            $rules['amount'] = 'required|numeric|min:0.01|max:'.$invoice->remaining_balance;
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Check if invoice is already paid
        if ($invoice->status === 'paid') {
            return back()->withErrors(['invoice' => 'This invoice is already paid.'])->withInput();
        }

        // Get the appropriate payment gateway
        $gateway = $this->getPaymentGateway($request->payment_method);

        // Process the payment
        $result = $gateway->processPayment($invoice, $request->all());

        if ($result['success']) {
            return redirect()->route('invoices.show', $invoice->id)
                ->with('success', $result['message']);
        } else {
            $errors = isset($result['errors']) ? $result['errors'] : [$result['message']];

            return back()->withErrors(['payment' => $errors])->withInput();
        }
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment)
    {
        $payment->load(['invoice.customer', 'confirmedBy']);

        return Inertia::render('payments/show', [
            'payment' => $payment,
        ]);
    }

    /**
     * Confirm a pending cash payment.
     */
    public function confirm(Request $request, Payment $payment)
    {
        if ($payment->payment_method !== 'cash') {
            return back()->withErrors(['payment' => 'Only cash payments can be manually confirmed.']);
        }

        if ($payment->status !== 'pending') {
            return back()->withErrors(['payment' => 'Only pending payments can be confirmed.']);
        }

        $gateway = new CashPaymentGateway;
        $result = $gateway->confirmPayment($payment, Auth::id());

        if ($result['success']) {
            return back()->with('success', $result['message']);
        } else {
            return back()->withErrors(['payment' => $result['message']]);
        }
    }

    /**
     * Get pending cash payments for admin confirmation.
     */
    public function pendingCashPayments()
    {
        $pendingPayments = Payment::with(['invoice.customer'])
            ->where('payment_method', 'cash')
            ->where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return Inertia::render('payments/pending-cash', [
            'payments' => $pendingPayments,
        ]);
    }

    /**
     * Get the appropriate payment gateway based on payment method.
     */
    private function getPaymentGateway(string $paymentMethod): PaymentGatewayInterface
    {
        return match ($paymentMethod) {
            'cash' => new CashPaymentGateway,
            'mpesa' => new MpesaPaymentGateway,
            default => throw new \InvalidArgumentException("Unsupported payment method: {$paymentMethod}"),
        };
    }

    /**
     * Export payments data as CSV.
     */
    public function export(Request $request)
    {
        $dateRange = $this->getDateRange($request);

        $payments = Payment::with(['invoice.customer', 'confirmedBy'])
            ->whereBetween('payment_date', $dateRange)
            ->orderBy('payment_date', 'desc')
            ->get();

        $filename = 'payments_'.Carbon::now()->format('Y-m-d_H-i-s').'.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($payments) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Payment ID',
                'Customer Name',
                'Customer Email',
                'Invoice Number',
                'Payment Method',
                'Amount',
                'Status',
                'Payment Date',
                'Transaction ID',
                'Reference Number',
                'Confirmed By',
                'Notes',
                'Created At',
            ]);

            // CSV data
            foreach ($payments as $payment) {
                fputcsv($file, [
                    $payment->id,
                    $payment->invoice->customer->name,
                    $payment->invoice->customer->email,
                    $payment->invoice->invoice_number,
                    ucfirst($payment->payment_method),
                    $payment->amount,
                    ucfirst($payment->status),
                    $payment->payment_date ? $payment->payment_date->format('Y-m-d H:i:s') : '',
                    $payment->transaction_id ?? '',
                    $payment->reference_number ?? '',
                    $payment->confirmedBy?->name ?? 'System',
                    $payment->notes ?? '',
                    $payment->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
