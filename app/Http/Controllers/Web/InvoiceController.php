<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Subscription;
use App\Models\SystemSetting;
use App\Services\CurrencyService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Invoice::query()
            ->with(['customer', 'subscription']);

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($q) use ($search) {
                        $q->where('name', 'like', "%{$search}%");
                    });
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('from_date')) {
            $query->whereDate('issue_date', '>=', $request->input('from_date'));
        }

        if ($request->filled('to_date')) {
            $query->whereDate('issue_date', '<=', $request->input('to_date'));
        }

        $invoices = $query->orderBy('issue_date', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('invoices/index', [
            'invoices' => $invoices,
            'filters' => $request->only(['search', 'status', 'from_date', 'to_date']),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $customers = Customer::select('id', 'name', 'email')
            ->orderBy('name')
            ->get();

        return Inertia::render('invoices/create', [
            'customers' => $customers,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|exists:customers,id',
            'subscription_id' => 'nullable|exists:subscriptions,id',
            'invoice_number' => 'required|string|max:50|unique:invoices,invoice_number',
            'issue_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:issue_date',
            'total_amount' => 'required|numeric|min:0',
            'status' => 'required|in:draft,pending,paid,overdue,cancelled',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.description' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Calculate the amount (subtotal) from items
        $amount = 0;
        foreach ($data['items'] as $item) {
            $amount += $item['quantity'] * $item['unit_price'];
        }

        // Create the invoice
        $invoice = Invoice::create([
            'customer_id' => $data['customer_id'],
            'subscription_id' => $data['subscription_id'] ?? null,
            'invoice_number' => $data['invoice_number'],
            'issue_date' => $data['issue_date'],
            'due_date' => $data['due_date'],
            'amount' => $amount,
            'tax_amount' => 0, // Default to 0 if not provided
            'total_amount' => $data['total_amount'],
            'status' => $data['status'],
            'notes' => $data['notes'] ?? null,
        ]);

        // Create invoice items
        foreach ($data['items'] as $item) {
            $subtotal = $item['quantity'] * $item['unit_price'];
            $invoice->items()->create([
                'description' => $item['description'],
                'quantity' => $item['quantity'],
                'unit_price' => $item['unit_price'],
                'subtotal' => $subtotal,
                'tax_rate' => 0, // Default to 0 if not provided
                'tax_amount' => 0, // Default to 0 if not provided
                'total' => $subtotal,
            ]);
        }

        return redirect()->route('invoices.show', $invoice->id)
            ->with('success', 'Invoice created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Invoice $invoice)
    {
        $invoice->load(['customer', 'subscription', 'items', 'payments.confirmedBy']);

        // Add computed attributes
        $invoice->append(['total_paid', 'remaining_balance']);

        return Inertia::render('invoices/show', [
            'invoice' => $invoice,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Invoice $invoice)
    {
        $invoice->load(['customer', 'subscription', 'items']);

        $customers = Customer::select('id', 'name', 'email')
            ->orderBy('name')
            ->get();

        $subscriptions = [];
        if ($invoice->customer_id) {
            $subscriptions = Subscription::where('customer_id', $invoice->customer_id)
                ->select('id', 'name', 'price', 'billing_cycle')
                ->get();
        }

        return Inertia::render('invoices/edit', [
            'invoice' => $invoice,
            'customers' => $customers,
            'subscriptions' => $subscriptions,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Invoice $invoice)
    {
        $validator = Validator::make($request->all(), [
            'subscription_id' => 'nullable|exists:subscriptions,id',
            'invoice_number' => 'required|string|max:50|unique:invoices,invoice_number,'.$invoice->id,
            'issue_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:issue_date',
            'total_amount' => 'required|numeric|min:0',
            'status' => 'required|in:draft,pending,paid,overdue,cancelled',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.id' => 'nullable|exists:invoice_items,id',
            'items.*.description' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Calculate the amount (subtotal) from items
        $amount = 0;
        foreach ($data['items'] as $item) {
            $amount += $item['quantity'] * $item['unit_price'];
        }

        // Update the invoice
        $invoice->update([
            'subscription_id' => $data['subscription_id'] ?? null,
            'invoice_number' => $data['invoice_number'],
            'issue_date' => $data['issue_date'],
            'due_date' => $data['due_date'],
            'amount' => $amount,
            'total_amount' => $data['total_amount'],
            'status' => $data['status'],
            'notes' => $data['notes'] ?? null,
        ]);

        // Get existing item IDs
        $existingItemIds = $invoice->items->pluck('id')->toArray();
        $updatedItemIds = [];

        // Update or create invoice items
        foreach ($data['items'] as $item) {
            if (isset($item['id'])) {
                // Update existing item
                $subtotal = $item['quantity'] * $item['unit_price'];
                $invoice->items()->where('id', $item['id'])->update([
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'subtotal' => $subtotal,
                    'tax_amount' => 0, // Default to 0 if not provided
                    'total' => $subtotal,
                ]);
                $updatedItemIds[] = $item['id'];
            } else {
                // Create new item
                $subtotal = $item['quantity'] * $item['unit_price'];
                $newItem = $invoice->items()->create([
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'subtotal' => $subtotal,
                    'tax_rate' => 0, // Default to 0 if not provided
                    'tax_amount' => 0, // Default to 0 if not provided
                    'total' => $subtotal,
                ]);
                $updatedItemIds[] = $newItem->id;
            }
        }

        // Delete items that were not updated
        $itemsToDelete = array_diff($existingItemIds, $updatedItemIds);
        if (! empty($itemsToDelete)) {
            $invoice->items()->whereIn('id', $itemsToDelete)->delete();
        }

        return redirect()->route('invoices.show', $invoice->id)
            ->with('success', 'Invoice updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Invoice $invoice)
    {
        // Delete invoice items first
        $invoice->items()->delete();

        // Then delete the invoice
        $invoice->delete();

        return redirect()->route('invoices.index')
            ->with('success', 'Invoice deleted successfully.');
    }

    /**
     * Download invoice as PDF.
     */
    public function downloadPdf(Invoice $invoice)
    {
        try {
            // Load invoice relationships
            $invoice->load(['customer', 'subscription', 'items', 'payments.confirmedBy']);

            // Add computed attributes
            $invoice->append(['total_paid', 'remaining_balance']);

            // Get company information from system settings or environment
            $companyInfo = $this->getCompanyInfo();

            // Create a currency formatting closure for the template
            $formatCurrency = function ($amount) {
                return formatCurrency($amount);
            };

            // Generate PDF
            $pdf = Pdf::loadView('invoices.pdf', [
                'invoice' => $invoice,
                'companyInfo' => $companyInfo,
                'formatCurrency' => $formatCurrency,
            ]);

            // Set PDF options
            $pdf->setPaper('A4', 'portrait');
            $pdf->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'DejaVu Sans',
            ]);

            // Generate filename
            $filename = 'invoice-' . $invoice->invoice_number . '.pdf';

            // Return PDF download
            return $pdf->download($filename);

        } catch (\Exception $e) {
            // Log the error
            \Log::error('PDF generation failed for invoice ' . $invoice->id, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Return error response
            return redirect()->back()
                ->with('error', 'Failed to generate PDF. Please try again or contact support.');
        }
    }

    /**
     * Get company information for PDF header.
     */
    private function getCompanyInfo(): array
    {
        return [
            'name' => env('ISP_COMPANY_NAME', env('APP_NAME', 'ISP Management System')),
            'address' => SystemSetting::get('company_address', ''),
            'email' => env('ISP_ADMIN_EMAIL', env('MAIL_FROM_ADDRESS', '')),
            'phone' => SystemSetting::get('company_phone', ''),
            'website' => env('APP_URL', ''),
        ];
    }
}
